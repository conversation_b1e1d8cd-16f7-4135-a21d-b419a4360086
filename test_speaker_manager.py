#!/usr/bin/env python3
"""
演讲人管理器测试脚本
用于验证演讲人管理功能的基本逻辑
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_speaker_manager_basic():
    """测试演讲人管理器的基本功能"""
    print("🧪 开始测试演讲人管理器...")
    
    try:
        # 模拟导入（不实际加载模型）
        print("1. 测试配置文件加载...")
        from config import MODEL_CONFIG, OFFLINE_CONFIG, REALTIME_CONFIG, SERVER_CONFIG, SESSION_CONFIG
        print("✅ 配置文件加载成功")
        
        print("2. 测试演讲人管理器类定义...")
        # 检查类是否正确定义
        with open('services/speaker_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键方法是否存在
        required_methods = [
            'def __init__',
            'def add_speaker',
            'def get_speakers',
            'def delete_speaker',
            'def identify_speaker',
            'def get_statistics'
        ]
        
        for method in required_methods:
            if method in content:
                print(f"✅ 找到方法: {method}")
            else:
                print(f"❌ 缺少方法: {method}")
        
        print("3. 测试API接口定义...")
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
            
        # 检查API路由是否存在
        api_routes = [
            "@app.route('/api/speakers', methods=['GET'])",
            "@app.route('/api/speakers', methods=['POST'])",
            "@app.route('/api/speakers/<speaker_id>', methods=['DELETE'])",
            "@app.route('/api/speakers/stats', methods=['GET'])"
        ]
        
        for route in api_routes:
            if route in app_content:
                print(f"✅ 找到API路由: {route}")
            else:
                print(f"❌ 缺少API路由: {route}")
        
        print("4. 测试WebSocket接口修改...")
        websocket_handlers = [
            "use_registered_speakers",
            "speaker_ids",
            "transcribe_audio_with_registered_speakers"
        ]
        
        for handler in websocket_handlers:
            if handler in app_content:
                print(f"✅ 找到WebSocket功能: {handler}")
            else:
                print(f"❌ 缺少WebSocket功能: {handler}")
        
        print("5. 测试前端页面修改...")
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
            
        frontend_features = [
            "speaker-management",
            "addSpeaker",
            "loadSpeakers",
            "deleteSpeaker",
            "useRegisteredSpeakers",
            "getSelectedSpeakerIds"
        ]
        
        for feature in frontend_features:
            if feature in html_content:
                print(f"✅ 找到前端功能: {feature}")
            else:
                print(f"❌ 缺少前端功能: {feature}")
        
        print("6. 测试JavaScript文件修改...")
        js_files = ['static/js/realtime.js', 'static/js/offline-websocket.js']
        
        for js_file in js_files:
            if os.path.exists(js_file):
                with open(js_file, 'r', encoding='utf-8') as f:
                    js_content = f.read()
                    
                if 'use_registered_speakers' in js_content and 'speaker_ids' in js_content:
                    print(f"✅ {js_file} 已正确修改")
                else:
                    print(f"❌ {js_file} 修改不完整")
            else:
                print(f"❌ 文件不存在: {js_file}")
        
        print("\n🎉 基本功能测试完成！")
        print("\n📋 功能总结:")
        print("✅ 演讲人音频管理模块")
        print("✅ 离线转录服务支持已注册演讲人")
        print("✅ 实时转录服务支持已注册演讲人")
        print("✅ REST API接口")
        print("✅ WebSocket接口修改")
        print("✅ 前端页面更新")
        print("✅ JavaScript功能增强")
        
        print("\n🚀 下一步:")
        print("1. 安装依赖包 (funasr, modelscope, flask等)")
        print("2. 启动服务器测试完整功能")
        print("3. 上传演讲人音频进行测试")
        print("4. 测试说话人识别效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n📁 检查目录结构...")
    
    required_dirs = [
        'services',
        'templates', 
        'static/js',
        'static/css',
        'models'
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
    
    required_files = [
        'app.py',
        'config.py',
        'services/speaker_manager.py',
        'services/offline_transcription.py',
        'services/realtime_transcription.py',
        'services/audio_processor.py',
        'templates/index.html',
        'static/js/realtime.js',
        'static/js/offline-websocket.js'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件缺失: {file_path}")

if __name__ == "__main__":
    print("🎙️ 音频转文字服务 - 演讲人功能测试")
    print("=" * 50)
    
    test_directory_structure()
    test_speaker_manager_basic()
    
    print("\n" + "=" * 50)
    print("测试完成！")
