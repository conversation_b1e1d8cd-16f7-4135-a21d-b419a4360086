// 实时转录相关变量
let realtimeSocket = null;
let mediaRecorder = null;
let audioStream = null;
let isRecording = false;
let audioChunks = [];
let realtimeHistory = [];  // 新增：保存历史转录结果

function startRealtime() {
    console.log('🎤 开始实时转录');

    // 清空历史记录
    realtimeHistory = [];

    // 检查Socket.IO是否加载
    if (typeof io === 'undefined') {
        console.error('❌ Socket.IO未加载');
        document.getElementById('realtimeResult').innerHTML = '<div class="error">❌ Socket.IO库未加载，请刷新页面重试</div>';
        return;
    }

    if (isRecording) {
        document.getElementById('realtimeResult').innerHTML = '<div class="warning">正在录音中...</div>';
        return;
    }

    const enableSpeaker = document.getElementById('enableRealtimeSpeaker').checked;
    const useRegisteredSpeakers = document.getElementById('useRealtimeRegisteredSpeakers').checked;
    console.log('🎭 启用说话人分离:', enableSpeaker);
    console.log('👥 使用已注册演讲人:', useRegisteredSpeakers);

    // 获取选中的演讲人ID
    let selectedSpeakerIds = [];
    if (useRegisteredSpeakers && typeof getSelectedSpeakerIds === 'function') {
        selectedSpeakerIds = getSelectedSpeakerIds('realtimeSpeakerCheckboxes');
        console.log('🎯 选中的演讲人:', selectedSpeakerIds);
    }

    // 更新UI状态
    document.getElementById('startBtn').disabled = true;
    document.getElementById('stopBtn').disabled = false;
    if (document.getElementById('clearBtn')) {
        document.getElementById('clearBtn').disabled = false;
    }
    document.getElementById('realtimeResult').innerHTML = '<div class="info">🔄 正在请求麦克风权限...</div>';

    // 请求麦克风权限
    navigator.mediaDevices.getUserMedia({
        audio: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true
        }
    })
    .then(function(stream) {
        console.log('✅ 麦克风权限获取成功');
        audioStream = stream;

        // 创建Socket连接
        realtimeSocket = io();

        // 连接成功
        realtimeSocket.on('connect', function() {
            console.log('✅ WebSocket连接成功');
            document.getElementById('realtimeResult').innerHTML = '<div class="info">🔄 正在初始化录音...</div>';

            // 开始录音
            startRecording(enableSpeaker, useRegisteredSpeakers, selectedSpeakerIds);
        });

        // 连接失败
        realtimeSocket.on('connect_error', function(error) {
            console.error('❌ WebSocket连接失败:', error);
            document.getElementById('realtimeResult').innerHTML = '<div class="error">❌ WebSocket连接失败</div>';
            stopRealtime();
        });

        // 实时转录开始
        realtimeSocket.on('realtime_started', function(data) {
            console.log('🎤 实时转录会话已创建:', data);
            document.getElementById('realtimeResult').innerHTML = '<div class="success">🎤 正在录音，请开始说话...</div>';
        });

        // 实时转录结果
        realtimeSocket.on('realtime_result', function(data) {
            console.log('📝 实时转录结果:', data);
            addRealtimeResult(data.data);  // 修改：使用累积显示函数
        });

        // 错误处理
        realtimeSocket.on('realtime_error', function(data) {
            console.error('❌ 实时转录错误:', data.message);
            document.getElementById('realtimeResult').innerHTML += `<div class="error">❌ 转录错误: ${data.message}</div>`;
        });

        // 会话结束
        realtimeSocket.on('realtime_stopped', function(data) {
            console.log('🛑 实时转录已停止:', data.message);
            // 不要在这里添加内容，让stopRealtime函数处理最终状态
        });
    })
    .catch(function(error) {
        console.error('❌ 麦克风权限获取失败:', error);
        document.getElementById('realtimeResult').innerHTML = '<div class="error">❌ 无法访问麦克风，请检查权限设置</div>';
        stopRealtime();
    });
}

function startRecording(enableSpeaker, useRegisteredSpeakers = false, selectedSpeakerIds = []) {
    try {
        // 创建AudioContext进行音频处理
        const audioContext = new (window.AudioContext || window.webkitAudioContext)({
            sampleRate: 16000
        });

        const source = audioContext.createMediaStreamSource(audioStream);
        const processor = audioContext.createScriptProcessor(4096, 1, 1);

        processor.onaudioprocess = function(e) {
            const inputData = e.inputBuffer.getChannelData(0);
            const audioArray = new Float32Array(inputData);

            // 发送音频数据
            realtimeSocket.emit('audio_chunk', {
                audio_data: Array.from(audioArray)  // 转换为数组发送
            });
        };

        source.connect(processor);
        processor.connect(audioContext.destination);

        // 发送开始信号
        realtimeSocket.emit('start_realtime', {
            enable_speaker: enableSpeaker,
            use_registered_speakers: useRegisteredSpeakers,
            speaker_ids: selectedSpeakerIds
        });

        isRecording = true;
        console.log('🎤 录音已开始');

        // 保存引用以便停止时清理
        window.audioContext = audioContext;
        window.audioProcessor = processor;

    } catch (error) {
        console.error('❌ 录音启动失败:', error);
        document.getElementById('realtimeResult').innerHTML = '<div class="error">❌ 录音启动失败</div>';
        stopRealtime();
    }
}

function addRealtimeResult(data) {
    // 添加新结果到历史记录
    const timestamp = new Date().toLocaleTimeString();

    if (data.segmented_text && data.segmented_text.length > 0) {
        // 说话人分离模式
        data.segmented_text.forEach(segment => {
            realtimeHistory.push({
                type: 'speaker',
                timestamp: timestamp,
                speaker: segment.speaker,
                start_time: segment.start_time,
                end_time: segment.end_time,
                text: segment.text
            });
        });
    } else if (data.voice_text && data.voice_text.trim()) {
        // 普通模式
        realtimeHistory.push({
            type: 'simple',
            timestamp: timestamp,
            text: data.voice_text.trim()
        });
    }

    // 显示完整历史记录
    displayRealtimeHistory();
}

function displayRealtimeHistory() {
    const resultDiv = document.getElementById('realtimeResult');

    let html = '';

    // 根据录音状态显示不同的标题
    if (isRecording) {
        html += '<div class="success">🎤 实时转录中...</div>';
    } else {
        html += '<div class="info">🛑 转录已停止</div>';
    }

    if (realtimeHistory.length === 0) {
        if (isRecording) {
            html += '<div class="info">等待语音输入...</div>';
        } else {
            html += '<div class="info">没有转录记录</div>';
        }
    } else {
        html += '<div class="realtime-history">';

        realtimeHistory.forEach((item, index) => {
            if (item.type === 'speaker') {
                const speakerClass = item.speaker.replace('_', '-').toLowerCase();
                html += `<div class="segment ${speakerClass}" data-index="${index}">
                    <span class="speaker-tag">${item.speaker}</span>
                    <span class="time-tag">(${item.start_time.toFixed(1)}s-${item.end_time.toFixed(1)}s)</span>
                    <span class="timestamp-tag">[${item.timestamp}]</span>:
                    ${item.text}
                </div>`;
            } else {
                html += `<div class="simple-segment" data-index="${index}">
                    <span class="timestamp-tag">[${item.timestamp}]</span>:
                    ${item.text}
                </div>`;
            }
        });

        html += '</div>';

        // 添加统计信息
        html += `<div class="realtime-stats">
            <small>📊 已转录 ${realtimeHistory.length} 段内容</small>
        </div>`;
    }

    resultDiv.innerHTML = html;

    // 自动滚动到底部
    resultDiv.scrollTop = resultDiv.scrollHeight;
}

// 修改原来的 displayRealtimeResult 函数为兼容性函数
function displayRealtimeResult(data) {
    // 为了兼容性，调用新的累积显示函数
    addRealtimeResult(data);
}

function clearRealtimeHistory() {
    console.log('🗑️ 清空实时转录历史记录');
    realtimeHistory = [];
    displayRealtimeHistory();
}

function stopRealtime() {
    console.log('🛑 停止实时转录');

    isRecording = false;

    // 清理音频处理器
    if (window.audioProcessor) {
        window.audioProcessor.disconnect();
        window.audioProcessor = null;
    }

    // 关闭音频上下文
    if (window.audioContext) {
        window.audioContext.close();
        window.audioContext = null;
    }

    // 关闭音频流
    if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
        audioStream = null;
    }

    // 发送停止信号
    if (realtimeSocket) {
        realtimeSocket.emit('stop_realtime');
        realtimeSocket.disconnect();
        realtimeSocket = null;
    }

    // 重置UI状态
    document.getElementById('startBtn').disabled = false;
    document.getElementById('stopBtn').disabled = true;

    // 清空按钮的状态：如果有历史记录就启用，否则禁用
    if (document.getElementById('clearBtn')) {
        document.getElementById('clearBtn').disabled = realtimeHistory.length === 0;
    }

    // 更新显示状态
    displayRealtimeHistory();

    // 显示最终统计
    if (realtimeHistory.length > 0) {
        const resultDiv = document.getElementById('realtimeResult');
        let currentHtml = resultDiv.innerHTML;
        currentHtml += `<div class="info">📊 本次会话共转录 ${realtimeHistory.length} 段内容</div>`;
        resultDiv.innerHTML = currentHtml;
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (isRecording) {
        stopRealtime();
    }
});