"""
演讲人音频管理模块
负责演讲人音频的存储、管理和匹配功能
"""
import os
import json
import uuid
import numpy as np
import soundfile as sf
import tempfile
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from config import MODEL_CONFIG


class SpeakerManager:
    """演讲人音频管理器"""
    
    def __init__(self, storage_dir: str = "./speaker_profiles"):
        """
        初始化演讲人管理器
        
        Args:
            storage_dir: 演讲人音频存储目录
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        
        # 演讲人配置文件路径
        self.profiles_file = self.storage_dir / "profiles.json"
        
        # 加载现有配置
        self.profiles = self._load_profiles()
        
        # 说话人模型（延迟加载）
        self._speaker_model = None
        self._speaker_model_type = None
        
        print(f"📁 演讲人管理器初始化完成，存储目录: {self.storage_dir}")
    
    def _load_profiles(self) -> Dict:
        """加载演讲人配置文件"""
        if self.profiles_file.exists():
            try:
                with open(self.profiles_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载演讲人配置失败: {e}")
        
        return {
            "speakers": {},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    
    def _save_profiles(self):
        """保存演讲人配置文件"""
        try:
            self.profiles["updated_at"] = datetime.now().isoformat()
            with open(self.profiles_file, 'w', encoding='utf-8') as f:
                json.dump(self.profiles, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存演讲人配置失败: {e}")
            raise e
    
    def _get_speaker_model(self):
        """延迟加载说话人模型"""
        if self._speaker_model is None:
            try:
                print("🔧 加载说话人识别模型...")
                from modelscope.pipelines import pipeline
                from modelscope.utils.constant import Tasks
                
                self._speaker_model = pipeline(
                    task=Tasks.speaker_verification,
                    model=MODEL_CONFIG["speaker_model_path"],
                    device=MODEL_CONFIG["device"]
                )
                self._speaker_model_type = "3d_speaker_local"
                print("✅ 说话人识别模型加载成功")
            except Exception as e:
                print(f"❌ 说话人识别模型加载失败: {e}")
                raise e
        
        return self._speaker_model
    
    def _convert_audio_to_wav(self, audio_file, target_path: Path) -> Tuple[np.ndarray, int]:
        """转换音频文件为WAV格式并保存"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_input:
                audio_file.seek(0)
                temp_input.write(audio_file.read())
                temp_input_path = temp_input.name
            
            # 使用ffmpeg转换为16kHz单声道WAV
            cmd = [
                'ffmpeg', '-i', temp_input_path,
                '-ar', '16000', '-ac', '1', '-f', 'wav', '-y', str(target_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"ffmpeg转换失败: {result.stderr}")
            
            # 读取转换后的音频
            audio_data, sample_rate = sf.read(target_path)
            
            # 清理临时文件
            os.unlink(temp_input_path)
            
            return audio_data, sample_rate
            
        except Exception as e:
            # 清理可能存在的临时文件
            if 'temp_input_path' in locals() and os.path.exists(temp_input_path):
                os.unlink(temp_input_path)
            raise e
    
    def add_speaker(self, speaker_name: str, audio_file, description: str = "") -> Dict:
        """
        添加演讲人音频
        
        Args:
            speaker_name: 演讲人名称
            audio_file: 音频文件对象
            description: 描述信息
            
        Returns:
            Dict: 添加结果
        """
        try:
            # 生成唯一ID
            speaker_id = str(uuid.uuid4())
            
            # 检查演讲人名称是否已存在
            for existing_id, speaker_info in self.profiles["speakers"].items():
                if speaker_info["name"] == speaker_name:
                    return {
                        "success": False,
                        "message": f"演讲人名称 '{speaker_name}' 已存在",
                        "speaker_id": None
                    }
            
            # 创建演讲人目录
            speaker_dir = self.storage_dir / speaker_id
            speaker_dir.mkdir(exist_ok=True)
            
            # 保存音频文件
            audio_path = speaker_dir / "profile.wav"
            audio_data, sample_rate = self._convert_audio_to_wav(audio_file, audio_path)
            
            # 检查音频长度（至少需要1秒）
            if len(audio_data) < sample_rate:
                # 清理文件
                if audio_path.exists():
                    os.unlink(audio_path)
                speaker_dir.rmdir()
                
                return {
                    "success": False,
                    "message": "音频太短，至少需要1秒钟的音频",
                    "speaker_id": None
                }
            
            # 提取说话人特征（如果模型可用）
            speaker_embedding = None
            try:
                speaker_model = self._get_speaker_model()
                # 使用模型提取特征
                embedding_result = speaker_model(str(audio_path))
                if isinstance(embedding_result, dict) and 'embedding' in embedding_result:
                    speaker_embedding = embedding_result['embedding'].tolist()
                elif hasattr(embedding_result, 'tolist'):
                    speaker_embedding = embedding_result.tolist()
                print(f"✅ 提取演讲人特征成功: {speaker_name}")
            except Exception as e:
                print(f"⚠️ 提取演讲人特征失败: {e}")
            
            # 保存演讲人信息
            speaker_info = {
                "id": speaker_id,
                "name": speaker_name,
                "description": description,
                "audio_path": str(audio_path),
                "audio_duration": len(audio_data) / sample_rate,
                "sample_rate": sample_rate,
                "embedding": speaker_embedding,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            self.profiles["speakers"][speaker_id] = speaker_info
            self._save_profiles()
            
            print(f"✅ 演讲人添加成功: {speaker_name} (ID: {speaker_id})")
            
            return {
                "success": True,
                "message": "演讲人添加成功",
                "speaker_id": speaker_id,
                "speaker_info": {
                    "id": speaker_id,
                    "name": speaker_name,
                    "description": description,
                    "duration": speaker_info["audio_duration"]
                }
            }
            
        except Exception as e:
            print(f"❌ 添加演讲人失败: {e}")
            return {
                "success": False,
                "message": f"添加演讲人失败: {str(e)}",
                "speaker_id": None
            }

    def get_speakers(self) -> List[Dict]:
        """获取所有演讲人列表"""
        speakers = []
        for speaker_id, speaker_info in self.profiles["speakers"].items():
            speakers.append({
                "id": speaker_id,
                "name": speaker_info["name"],
                "description": speaker_info.get("description", ""),
                "duration": speaker_info.get("audio_duration", 0),
                "created_at": speaker_info.get("created_at", ""),
                "has_embedding": speaker_info.get("embedding") is not None
            })

        # 按创建时间排序
        speakers.sort(key=lambda x: x["created_at"], reverse=True)
        return speakers

    def get_speaker(self, speaker_id: str) -> Optional[Dict]:
        """获取指定演讲人信息"""
        return self.profiles["speakers"].get(speaker_id)

    def delete_speaker(self, speaker_id: str) -> Dict:
        """删除演讲人"""
        try:
            if speaker_id not in self.profiles["speakers"]:
                return {
                    "success": False,
                    "message": "演讲人不存在"
                }

            speaker_info = self.profiles["speakers"][speaker_id]
            speaker_name = speaker_info["name"]

            # 删除音频文件和目录
            speaker_dir = self.storage_dir / speaker_id
            if speaker_dir.exists():
                import shutil
                shutil.rmtree(speaker_dir)

            # 从配置中删除
            del self.profiles["speakers"][speaker_id]
            self._save_profiles()

            print(f"✅ 演讲人删除成功: {speaker_name} (ID: {speaker_id})")

            return {
                "success": True,
                "message": f"演讲人 '{speaker_name}' 删除成功"
            }

        except Exception as e:
            print(f"❌ 删除演讲人失败: {e}")
            return {
                "success": False,
                "message": f"删除演讲人失败: {str(e)}"
            }

    def identify_speaker(self, audio_data: np.ndarray, sample_rate: int, threshold: float = 0.7) -> Optional[str]:
        """
        识别音频中的演讲人

        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            threshold: 相似度阈值

        Returns:
            str: 匹配的演讲人ID，如果没有匹配则返回None
        """
        try:
            if not self.profiles["speakers"]:
                return None

            # 获取说话人模型
            speaker_model = self._get_speaker_model()

            # 保存临时音频文件用于特征提取
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                sf.write(temp_file.name, audio_data, sample_rate)
                temp_audio_path = temp_file.name

            try:
                # 提取当前音频的特征
                current_embedding_result = speaker_model(temp_audio_path)
                if isinstance(current_embedding_result, dict) and 'embedding' in current_embedding_result:
                    current_embedding = current_embedding_result['embedding']
                else:
                    current_embedding = current_embedding_result

                if hasattr(current_embedding, 'numpy'):
                    current_embedding = current_embedding.numpy()
                elif hasattr(current_embedding, 'tolist'):
                    current_embedding = np.array(current_embedding.tolist())

                best_match_id = None
                best_similarity = 0.0

                # 与所有已注册演讲人进行比较
                for speaker_id, speaker_info in self.profiles["speakers"].items():
                    if speaker_info.get("embedding") is None:
                        continue

                    stored_embedding = np.array(speaker_info["embedding"])

                    # 计算余弦相似度
                    similarity = self._calculate_cosine_similarity(current_embedding, stored_embedding)

                    if similarity > best_similarity and similarity >= threshold:
                        best_similarity = similarity
                        best_match_id = speaker_id

                if best_match_id:
                    speaker_name = self.profiles["speakers"][best_match_id]["name"]
                    print(f"🎯 识别到演讲人: {speaker_name} (相似度: {best_similarity:.3f})")
                    return best_match_id
                else:
                    print(f"❓ 未识别到匹配的演讲人 (最高相似度: {best_similarity:.3f})")
                    return None

            finally:
                # 清理临时文件
                if os.path.exists(temp_audio_path):
                    os.unlink(temp_audio_path)

        except Exception as e:
            print(f"❌ 演讲人识别失败: {e}")
            return None

    def _calculate_cosine_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算两个向量的余弦相似度"""
        try:
            # 确保向量是一维的
            if embedding1.ndim > 1:
                embedding1 = embedding1.flatten()
            if embedding2.ndim > 1:
                embedding2 = embedding2.flatten()

            # 计算余弦相似度
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = dot_product / (norm1 * norm2)
            return float(similarity)

        except Exception as e:
            print(f"❌ 计算相似度失败: {e}")
            return 0.0

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_speakers = len(self.profiles["speakers"])
        speakers_with_embedding = sum(1 for s in self.profiles["speakers"].values() if s.get("embedding") is not None)

        return {
            "total_speakers": total_speakers,
            "speakers_with_embedding": speakers_with_embedding,
            "storage_dir": str(self.storage_dir),
            "created_at": self.profiles.get("created_at", ""),
            "updated_at": self.profiles.get("updated_at", "")
        }
