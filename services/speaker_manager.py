"""
演讲人音频管理模块
负责演讲人音频的存储、管理和匹配功能
"""
import os
import json
import uuid
import time
import threading
import numpy as np
import soundfile as sf
import tempfile
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from collections import OrderedDict
from config import MODEL_CONFIG, SPEAKER_CONFIG

# 尝试导入psutil，如果失败则使用简化的内存监控
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    print("⚠️ psutil未安装，内存监控功能将受限")
    HAS_PSUTIL = False
    psutil = None


class SpeakerEmbeddingCache:
    """演讲人特征缓存管理器 - 使用LRU策略"""

    def __init__(self, max_size: int = 50, ttl: int = 3600):
        """
        初始化缓存

        Args:
            max_size: 最大缓存数量
            ttl: 缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache = OrderedDict()  # LRU缓存
        self.access_times = {}      # 访问时间记录
        self.lock = threading.RLock()  # 线程安全锁

    def get(self, speaker_id: str) -> Optional[np.ndarray]:
        """获取演讲人特征"""
        with self.lock:
            current_time = time.time()

            if speaker_id not in self.cache:
                return None

            # 检查是否过期
            if current_time - self.access_times[speaker_id] > self.ttl:
                self._remove(speaker_id)
                return None

            # 更新访问时间并移到末尾（LRU）
            embedding = self.cache.pop(speaker_id)
            self.cache[speaker_id] = embedding
            self.access_times[speaker_id] = current_time

            return embedding

    def put(self, speaker_id: str, embedding: np.ndarray):
        """存储演讲人特征"""
        with self.lock:
            current_time = time.time()

            # 如果已存在，更新
            if speaker_id in self.cache:
                self.cache.pop(speaker_id)

            # 检查缓存大小，如果满了则移除最旧的
            elif len(self.cache) >= self.max_size:
                oldest_id = next(iter(self.cache))
                self._remove(oldest_id)

            # 添加新的特征
            self.cache[speaker_id] = embedding.copy()
            self.access_times[speaker_id] = current_time

    def remove(self, speaker_id: str):
        """移除指定演讲人特征"""
        with self.lock:
            self._remove(speaker_id)

    def _remove(self, speaker_id: str):
        """内部移除方法（不加锁）"""
        if speaker_id in self.cache:
            del self.cache[speaker_id]
        if speaker_id in self.access_times:
            del self.access_times[speaker_id]

    def clear_expired(self):
        """清理过期的缓存"""
        with self.lock:
            current_time = time.time()
            expired_ids = []

            for speaker_id, access_time in self.access_times.items():
                if current_time - access_time > self.ttl:
                    expired_ids.append(speaker_id)

            for speaker_id in expired_ids:
                self._remove(speaker_id)

            return len(expired_ids)

    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        with self.lock:
            return {
                "cache_size": len(self.cache),
                "max_size": self.max_size,
                "ttl": self.ttl,
                "memory_usage_mb": self._estimate_memory_usage()
            }

    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        total_bytes = 0
        for embedding in self.cache.values():
            total_bytes += embedding.nbytes
        return total_bytes / (1024 * 1024)


class SpeakerManager:
    """演讲人音频管理器 - 带内存管理"""

    def __init__(self, storage_dir: str = "./speaker_profiles"):
        """
        初始化演讲人管理器

        Args:
            storage_dir: 演讲人音频存储目录
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)

        # 演讲人配置文件路径
        self.profiles_file = self.storage_dir / "profiles.json"

        # 加载现有配置
        self.profiles = self._load_profiles()

        # 说话人模型（延迟加载）
        self._speaker_model = None
        self._speaker_model_type = None

        # 特征缓存管理器
        self.embedding_cache = SpeakerEmbeddingCache(
            max_size=SPEAKER_CONFIG['max_cache_size'],
            ttl=SPEAKER_CONFIG['cache_ttl']
        )

        # 内存监控
        if HAS_PSUTIL:
            self.process = psutil.Process()
        else:
            self.process = None
        self.last_cleanup_time = time.time()

        # 启动自动清理线程
        if SPEAKER_CONFIG['enable_auto_cleanup']:
            self._start_cleanup_thread()

        print(f"📁 演讲人管理器初始化完成，存储目录: {self.storage_dir}")
        print(f"🧠 内存管理: 缓存大小={SPEAKER_CONFIG['max_cache_size']}, TTL={SPEAKER_CONFIG['cache_ttl']}秒")
    
    def _load_profiles(self) -> Dict:
        """加载演讲人配置文件"""
        if self.profiles_file.exists():
            try:
                with open(self.profiles_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载演讲人配置失败: {e}")
        
        return {
            "speakers": {},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    
    def _save_profiles(self):
        """保存演讲人配置文件"""
        try:
            self.profiles["updated_at"] = datetime.now().isoformat()
            with open(self.profiles_file, 'w', encoding='utf-8') as f:
                json.dump(self.profiles, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存演讲人配置失败: {e}")
            raise e
    
    def _get_speaker_model(self):
        """延迟加载说话人模型"""
        if self._speaker_model is None:
            try:
                print("🔧 加载说话人识别模型...")
                from modelscope.pipelines import pipeline
                from modelscope.utils.constant import Tasks
                
                self._speaker_model = pipeline(
                    task=Tasks.speaker_verification,
                    model=MODEL_CONFIG["speaker_model_path"],
                    device=MODEL_CONFIG["device"]
                )
                self._speaker_model_type = "3d_speaker_local"
                print("✅ 说话人识别模型加载成功")
            except Exception as e:
                print(f"❌ 说话人识别模型加载失败: {e}")
                raise e
        
        return self._speaker_model

    def _start_cleanup_thread(self):
        """启动自动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(SPEAKER_CONFIG['auto_cleanup_interval'])
                    self._perform_cleanup()
                except Exception as e:
                    print(f"❌ 自动清理线程异常: {e}")

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True, name="SpeakerCleanup")
        cleanup_thread.start()
        print("🧹 演讲人自动清理线程已启动")

    def _perform_cleanup(self):
        """执行清理操作"""
        current_time = time.time()

        # 清理过期缓存
        expired_count = self.embedding_cache.clear_expired()
        if expired_count > 0:
            print(f"🧹 清理了 {expired_count} 个过期的演讲人特征缓存")

        # 检查内存使用情况
        memory_info = self._get_memory_info()
        if memory_info['memory_usage_mb'] > SPEAKER_CONFIG['memory_threshold_mb']:
            print(f"⚠️ 内存使用超过阈值: {memory_info['memory_usage_mb']:.1f}MB > {SPEAKER_CONFIG['memory_threshold_mb']}MB")

            # 强制清理一半的缓存
            self._force_cleanup_cache(0.5)

        self.last_cleanup_time = current_time

    def _force_cleanup_cache(self, ratio: float = 0.5):
        """强制清理缓存"""
        with self.embedding_cache.lock:
            current_size = len(self.embedding_cache.cache)
            target_size = int(current_size * (1 - ratio))

            # 移除最旧的缓存项
            items_to_remove = current_size - target_size
            removed_ids = []

            for _ in range(items_to_remove):
                if self.embedding_cache.cache:
                    oldest_id = next(iter(self.embedding_cache.cache))
                    self.embedding_cache._remove(oldest_id)
                    removed_ids.append(oldest_id)

            if removed_ids:
                print(f"🧹 强制清理了 {len(removed_ids)} 个演讲人特征缓存")

    def _get_memory_info(self) -> Dict:
        """获取内存使用信息"""
        try:
            cache_stats = self.embedding_cache.get_stats()

            if HAS_PSUTIL and self.process:
                memory_info = self.process.memory_info()
                process_memory_mb = memory_info.rss / (1024 * 1024)
            else:
                process_memory_mb = 0

            return {
                "process_memory_mb": process_memory_mb,
                "cache_memory_mb": cache_stats["memory_usage_mb"],
                "memory_usage_mb": process_memory_mb,
                "cache_size": cache_stats["cache_size"],
                "max_cache_size": cache_stats["max_size"],
                "has_psutil": HAS_PSUTIL
            }
        except Exception as e:
            print(f"❌ 获取内存信息失败: {e}")
            return {
                "process_memory_mb": 0,
                "cache_memory_mb": 0,
                "memory_usage_mb": 0,
                "cache_size": 0,
                "max_cache_size": 0,
                "has_psutil": HAS_PSUTIL
            }
    
    def _convert_audio_to_wav(self, audio_file, target_path: Path) -> Tuple[np.ndarray, int]:
        """转换音频文件为WAV格式并保存"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_input:
                audio_file.seek(0)
                temp_input.write(audio_file.read())
                temp_input_path = temp_input.name
            
            # 使用ffmpeg转换为16kHz单声道WAV
            cmd = [
                'ffmpeg', '-i', temp_input_path,
                '-ar', '16000', '-ac', '1', '-f', 'wav', '-y', str(target_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"ffmpeg转换失败: {result.stderr}")
            
            # 读取转换后的音频
            audio_data, sample_rate = sf.read(target_path)
            
            # 清理临时文件
            os.unlink(temp_input_path)
            
            return audio_data, sample_rate
            
        except Exception as e:
            # 清理可能存在的临时文件
            if 'temp_input_path' in locals() and os.path.exists(temp_input_path):
                os.unlink(temp_input_path)
            raise e
    
    def add_speaker(self, speaker_name: str, audio_file, description: str = "") -> Dict:
        """
        添加演讲人音频
        
        Args:
            speaker_name: 演讲人名称
            audio_file: 音频文件对象
            description: 描述信息
            
        Returns:
            Dict: 添加结果
        """
        try:
            # 生成唯一ID
            speaker_id = str(uuid.uuid4())
            
            # 检查演讲人名称是否已存在
            for existing_id, speaker_info in self.profiles["speakers"].items():
                if speaker_info["name"] == speaker_name:
                    return {
                        "success": False,
                        "message": f"演讲人名称 '{speaker_name}' 已存在",
                        "speaker_id": None
                    }
            
            # 创建演讲人目录
            speaker_dir = self.storage_dir / speaker_id
            speaker_dir.mkdir(exist_ok=True)
            
            # 保存音频文件
            audio_path = speaker_dir / "profile.wav"
            audio_data, sample_rate = self._convert_audio_to_wav(audio_file, audio_path)
            
            # 检查音频长度（至少需要1秒）
            if len(audio_data) < sample_rate:
                # 清理文件
                if audio_path.exists():
                    os.unlink(audio_path)
                speaker_dir.rmdir()
                
                return {
                    "success": False,
                    "message": "音频太短，至少需要1秒钟的音频",
                    "speaker_id": None
                }
            
            # 提取说话人特征（如果模型可用）
            speaker_embedding = None
            speaker_embedding_array = None
            try:
                speaker_model = self._get_speaker_model()
                # 使用模型提取特征
                embedding_result = speaker_model(str(audio_path))

                if isinstance(embedding_result, dict) and 'embedding' in embedding_result:
                    speaker_embedding_array = embedding_result['embedding']
                elif hasattr(embedding_result, 'numpy'):
                    speaker_embedding_array = embedding_result.numpy()
                else:
                    speaker_embedding_array = np.array(embedding_result)

                # 转换为列表用于JSON存储
                if speaker_embedding_array is not None:
                    speaker_embedding = speaker_embedding_array.tolist()

                    # 存储到缓存中
                    self.embedding_cache.put(speaker_id, speaker_embedding_array)

                print(f"✅ 提取演讲人特征成功: {speaker_name}")
            except Exception as e:
                print(f"⚠️ 提取演讲人特征失败: {e}")
            
            # 保存演讲人信息
            speaker_info = {
                "id": speaker_id,
                "name": speaker_name,
                "description": description,
                "audio_path": str(audio_path),
                "audio_duration": len(audio_data) / sample_rate,
                "sample_rate": sample_rate,
                "embedding": speaker_embedding,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            self.profiles["speakers"][speaker_id] = speaker_info
            self._save_profiles()
            
            print(f"✅ 演讲人添加成功: {speaker_name} (ID: {speaker_id})")
            
            return {
                "success": True,
                "message": "演讲人添加成功",
                "speaker_id": speaker_id,
                "speaker_info": {
                    "id": speaker_id,
                    "name": speaker_name,
                    "description": description,
                    "duration": speaker_info["audio_duration"]
                }
            }
            
        except Exception as e:
            print(f"❌ 添加演讲人失败: {e}")
            return {
                "success": False,
                "message": f"添加演讲人失败: {str(e)}",
                "speaker_id": None
            }

    def get_speakers(self) -> List[Dict]:
        """获取所有演讲人列表"""
        speakers = []
        for speaker_id, speaker_info in self.profiles["speakers"].items():
            speakers.append({
                "id": speaker_id,
                "name": speaker_info["name"],
                "description": speaker_info.get("description", ""),
                "duration": speaker_info.get("audio_duration", 0),
                "created_at": speaker_info.get("created_at", ""),
                "has_embedding": speaker_info.get("embedding") is not None
            })

        # 按创建时间排序
        speakers.sort(key=lambda x: x["created_at"], reverse=True)
        return speakers

    def get_speaker(self, speaker_id: str) -> Optional[Dict]:
        """获取指定演讲人信息"""
        return self.profiles["speakers"].get(speaker_id)

    def delete_speaker(self, speaker_id: str) -> Dict:
        """删除演讲人"""
        try:
            if speaker_id not in self.profiles["speakers"]:
                return {
                    "success": False,
                    "message": "演讲人不存在"
                }

            speaker_info = self.profiles["speakers"][speaker_id]
            speaker_name = speaker_info["name"]

            # 删除音频文件和目录
            speaker_dir = self.storage_dir / speaker_id
            if speaker_dir.exists():
                import shutil
                shutil.rmtree(speaker_dir)

            # 从配置中删除
            del self.profiles["speakers"][speaker_id]
            self._save_profiles()

            # 从缓存中删除
            self.embedding_cache.remove(speaker_id)

            print(f"✅ 演讲人删除成功: {speaker_name} (ID: {speaker_id})")

            return {
                "success": True,
                "message": f"演讲人 '{speaker_name}' 删除成功"
            }

        except Exception as e:
            print(f"❌ 删除演讲人失败: {e}")
            return {
                "success": False,
                "message": f"删除演讲人失败: {str(e)}"
            }

    def identify_speaker(self, audio_data: np.ndarray, sample_rate: int, threshold: float = None) -> Optional[str]:
        """
        识别音频中的演讲人 - 使用缓存机制

        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            threshold: 相似度阈值，如果为None则使用配置中的默认值

        Returns:
            str: 匹配的演讲人ID，如果没有匹配则返回None
        """
        try:
            if not self.profiles["speakers"]:
                return None

            # 使用配置中的默认阈值
            if threshold is None:
                threshold = SPEAKER_CONFIG['similarity_threshold']

            # 获取说话人模型
            speaker_model = self._get_speaker_model()

            # 保存临时音频文件用于特征提取
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                sf.write(temp_file.name, audio_data, sample_rate)
                temp_audio_path = temp_file.name

            try:
                # 提取当前音频的特征
                current_embedding_result = speaker_model(temp_audio_path)
                if isinstance(current_embedding_result, dict) and 'embedding' in current_embedding_result:
                    current_embedding = current_embedding_result['embedding']
                else:
                    current_embedding = current_embedding_result

                if hasattr(current_embedding, 'numpy'):
                    current_embedding = current_embedding.numpy()
                elif hasattr(current_embedding, 'tolist'):
                    current_embedding = np.array(current_embedding.tolist())

                best_match_id = None
                best_similarity = 0.0

                # 与所有已注册演讲人进行比较
                for speaker_id, speaker_info in self.profiles["speakers"].items():
                    # 优先从缓存中获取特征
                    stored_embedding = self._get_speaker_embedding(speaker_id)
                    if stored_embedding is None:
                        continue

                    # 计算余弦相似度
                    similarity = self._calculate_cosine_similarity(current_embedding, stored_embedding)

                    if similarity > best_similarity and similarity >= threshold:
                        best_similarity = similarity
                        best_match_id = speaker_id

                if best_match_id:
                    speaker_name = self.profiles["speakers"][best_match_id]["name"]
                    print(f"🎯 识别到演讲人: {speaker_name} (相似度: {best_similarity:.3f})")
                    return best_match_id
                else:
                    print(f"❓ 未识别到匹配的演讲人 (最高相似度: {best_similarity:.3f})")
                    return None

            finally:
                # 清理临时文件
                if os.path.exists(temp_audio_path):
                    os.unlink(temp_audio_path)

        except Exception as e:
            print(f"❌ 演讲人识别失败: {e}")
            return None

    def _get_speaker_embedding(self, speaker_id: str) -> Optional[np.ndarray]:
        """
        获取演讲人特征 - 优先从缓存获取，支持延迟加载

        Args:
            speaker_id: 演讲人ID

        Returns:
            np.ndarray: 演讲人特征向量，如果不存在则返回None
        """
        # 首先尝试从缓存获取
        embedding = self.embedding_cache.get(speaker_id)
        if embedding is not None:
            return embedding

        # 如果缓存中没有，且启用了延迟加载，则从文件加载
        if SPEAKER_CONFIG['enable_lazy_loading']:
            speaker_info = self.profiles["speakers"].get(speaker_id)
            if speaker_info and speaker_info.get("embedding"):
                # 从JSON数据加载
                embedding_array = np.array(speaker_info["embedding"])

                # 存储到缓存
                self.embedding_cache.put(speaker_id, embedding_array)

                return embedding_array

        return None

    def _calculate_cosine_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算两个向量的余弦相似度"""
        try:
            # 确保向量是一维的
            if embedding1.ndim > 1:
                embedding1 = embedding1.flatten()
            if embedding2.ndim > 1:
                embedding2 = embedding2.flatten()

            # 计算余弦相似度
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = dot_product / (norm1 * norm2)
            return float(similarity)

        except Exception as e:
            print(f"❌ 计算相似度失败: {e}")
            return 0.0

    def get_statistics(self) -> Dict:
        """获取统计信息 - 包含内存使用情况"""
        total_speakers = len(self.profiles["speakers"])
        speakers_with_embedding = sum(1 for s in self.profiles["speakers"].values() if s.get("embedding") is not None)

        # 获取内存信息
        memory_info = self._get_memory_info()
        cache_stats = self.embedding_cache.get_stats()

        return {
            "total_speakers": total_speakers,
            "speakers_with_embedding": speakers_with_embedding,
            "storage_dir": str(self.storage_dir),
            "created_at": self.profiles.get("created_at", ""),
            "updated_at": self.profiles.get("updated_at", ""),
            "memory_info": memory_info,
            "cache_stats": cache_stats,
            "config": {
                "max_cache_size": SPEAKER_CONFIG['max_cache_size'],
                "cache_ttl": SPEAKER_CONFIG['cache_ttl'],
                "memory_threshold_mb": SPEAKER_CONFIG['memory_threshold_mb'],
                "enable_lazy_loading": SPEAKER_CONFIG['enable_lazy_loading'],
                "enable_auto_cleanup": SPEAKER_CONFIG['enable_auto_cleanup']
            },
            "last_cleanup_time": datetime.fromtimestamp(self.last_cleanup_time).isoformat()
        }

    def force_cleanup(self) -> Dict:
        """手动触发清理操作"""
        print("🧹 手动触发演讲人缓存清理...")

        before_stats = self.embedding_cache.get_stats()
        before_memory = self._get_memory_info()

        # 执行清理
        self._perform_cleanup()

        after_stats = self.embedding_cache.get_stats()
        after_memory = self._get_memory_info()

        result = {
            "before": {
                "cache_size": before_stats["cache_size"],
                "memory_mb": before_memory["memory_usage_mb"]
            },
            "after": {
                "cache_size": after_stats["cache_size"],
                "memory_mb": after_memory["memory_usage_mb"]
            },
            "cleaned": {
                "cache_items": before_stats["cache_size"] - after_stats["cache_size"],
                "memory_mb": before_memory["memory_usage_mb"] - after_memory["memory_usage_mb"]
            }
        }

        print(f"✅ 清理完成: 释放了 {result['cleaned']['cache_items']} 个缓存项，{result['cleaned']['memory_mb']:.1f}MB 内存")
        return result

    def preload_speakers(self, speaker_ids: List[str] = None) -> int:
        """
        预加载演讲人特征到缓存

        Args:
            speaker_ids: 要预加载的演讲人ID列表，如果为None则加载所有

        Returns:
            int: 成功预加载的数量
        """
        if speaker_ids is None:
            speaker_ids = list(self.profiles["speakers"].keys())

        loaded_count = 0
        for speaker_id in speaker_ids:
            if self._get_speaker_embedding(speaker_id) is not None:
                loaded_count += 1

        print(f"📥 预加载了 {loaded_count}/{len(speaker_ids)} 个演讲人特征")
        return loaded_count
