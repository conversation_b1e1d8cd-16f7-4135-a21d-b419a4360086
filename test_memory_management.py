#!/usr/bin/env python3
"""
内存管理功能测试脚本
测试演讲人特征的缓存、清理和内存监控功能
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_embedding_cache():
    """测试演讲人特征缓存"""
    print("🧪 测试演讲人特征缓存...")
    
    try:
        from services.speaker_manager import SpeakerEmbeddingCache
        
        # 创建小容量缓存用于测试
        cache = SpeakerEmbeddingCache(max_size=3, ttl=2)  # 3个项目，2秒TTL
        
        # 测试添加和获取
        print("1. 测试缓存添加和获取...")
        embedding1 = np.random.rand(512).astype(np.float32)
        embedding2 = np.random.rand(512).astype(np.float32)
        embedding3 = np.random.rand(512).astype(np.float32)
        
        cache.put("speaker1", embedding1)
        cache.put("speaker2", embedding2)
        cache.put("speaker3", embedding3)
        
        # 验证获取
        retrieved = cache.get("speaker1")
        if retrieved is not None and np.array_equal(retrieved, embedding1):
            print("✅ 缓存存储和获取正常")
        else:
            print("❌ 缓存存储或获取失败")
        
        # 测试LRU机制
        print("2. 测试LRU机制...")
        embedding4 = np.random.rand(512).astype(np.float32)
        cache.put("speaker4", embedding4)  # 应该移除最旧的speaker1
        
        if cache.get("speaker1") is None and cache.get("speaker4") is not None:
            print("✅ LRU机制工作正常")
        else:
            print("❌ LRU机制异常")
        
        # 测试TTL过期
        print("3. 测试TTL过期机制...")
        print("等待3秒让缓存过期...")
        time.sleep(3)
        
        expired_count = cache.clear_expired()
        if expired_count > 0:
            print(f"✅ TTL过期机制正常，清理了 {expired_count} 个过期项")
        else:
            print("⚠️ TTL过期机制可能异常或缓存已被访问")
        
        # 测试统计信息
        print("4. 测试统计信息...")
        stats = cache.get_stats()
        print(f"缓存统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_speaker_manager_memory():
    """测试演讲人管理器的内存管理功能"""
    print("\n🧪 测试演讲人管理器内存管理...")
    
    try:
        # 模拟配置
        from config import SPEAKER_CONFIG
        print(f"内存管理配置: {SPEAKER_CONFIG}")
        
        # 检查配置项
        required_configs = [
            'max_cache_size', 'cache_ttl', 'memory_threshold_mb',
            'enable_lazy_loading', 'enable_auto_cleanup'
        ]
        
        for config_key in required_configs:
            if config_key in SPEAKER_CONFIG:
                print(f"✅ 配置项 {config_key}: {SPEAKER_CONFIG[config_key]}")
            else:
                print(f"❌ 缺少配置项: {config_key}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演讲人管理器测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点定义"""
    print("\n🧪 测试API端点定义...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # 检查内存管理相关的API端点
        api_endpoints = [
            "/api/speakers/cleanup",
            "/api/speakers/preload", 
            "/api/system/memory",
            "/api/speakers/stats"
        ]
        
        for endpoint in api_endpoints:
            if endpoint in app_content:
                print(f"✅ API端点存在: {endpoint}")
            else:
                print(f"❌ API端点缺失: {endpoint}")
        
        # 检查内存管理相关的函数
        functions = [
            "cleanup_speakers",
            "preload_speakers",
            "get_memory_status",
            "get_speaker_stats"
        ]
        
        for func in functions:
            if f"def {func}" in app_content:
                print(f"✅ 函数存在: {func}")
            else:
                print(f"❌ 函数缺失: {func}")
        
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def test_frontend_features():
    """测试前端功能"""
    print("\n🧪 测试前端功能...")
    
    try:
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查内存管理相关的前端功能
        frontend_features = [
            "cleanupCacheBtn",
            "preloadSpeakersBtn", 
            "refreshMemoryBtn",
            "memoryStatus",
            "loadMemoryStatus",
            "cleanupCache",
            "preloadSpeakers"
        ]
        
        for feature in frontend_features:
            if feature in html_content:
                print(f"✅ 前端功能存在: {feature}")
            else:
                print(f"❌ 前端功能缺失: {feature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端功能测试失败: {e}")
        return False

def test_graceful_degradation():
    """测试优雅降级"""
    print("\n🧪 测试优雅降级...")
    
    try:
        # 测试psutil导入处理
        with open('services/speaker_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "HAS_PSUTIL" in content and "try:" in content and "except ImportError:" in content:
            print("✅ psutil优雅降级处理存在")
        else:
            print("❌ psutil优雅降级处理缺失")
        
        # 测试配置默认值
        from config import SPEAKER_CONFIG
        if all(key in SPEAKER_CONFIG for key in ['max_cache_size', 'cache_ttl', 'memory_threshold_mb']):
            print("✅ 配置默认值完整")
        else:
            print("❌ 配置默认值不完整")
        
        return True
        
    except Exception as e:
        print(f"❌ 优雅降级测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎙️ 演讲人音频内存管理功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("演讲人特征缓存", test_embedding_cache()))
    test_results.append(("演讲人管理器内存管理", test_speaker_manager_memory()))
    test_results.append(("API端点定义", test_api_endpoints()))
    test_results.append(("前端功能", test_frontend_features()))
    test_results.append(("优雅降级", test_graceful_degradation()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有内存管理功能测试通过！")
        print("\n📋 功能特性:")
        print("✅ LRU缓存机制")
        print("✅ TTL过期清理")
        print("✅ 延迟加载")
        print("✅ 自动清理线程")
        print("✅ 内存监控")
        print("✅ API接口")
        print("✅ 前端管理界面")
        print("✅ 优雅降级")
        
        print("\n🚀 建议:")
        print("1. 安装依赖: pip install psutil (可选，用于精确内存监控)")
        print("2. 根据服务器配置调整 SPEAKER_CONFIG 中的参数")
        print("3. 在生产环境中监控内存使用情况")
        print("4. 定期使用清理功能释放内存")
    else:
        print("⚠️ 部分测试失败，请检查相关功能实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
