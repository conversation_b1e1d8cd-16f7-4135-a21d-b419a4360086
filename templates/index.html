<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频转文字服务</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }

        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .section h2 {
            color: #007bff;
            margin-top: 0;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input[type="file"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        input[type="checkbox"] {
            margin-right: 8px;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: background-color 0.3s;
        }

        button:hover:not(:disabled) {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #stopBtn, #cancelOfflineBtn {
            background-color: #dc3545;
        }

        #stopBtn:hover:not(:disabled), #cancelOfflineBtn:hover:not(:disabled) {
            background-color: #c82333;
        }

        #clearBtn {
            background-color: #6c757d;
        }

        #clearBtn:hover:not(:disabled) {
            background-color: #545b62;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            min-height: 100px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .warning {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .speaker-result {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
        }

        .segment {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
            background-color: white;
            word-wrap: break-word;
        }

        .segment.speaker-0 {
            border-left: 4px solid #007bff;
        }

        .segment.speaker-1 {
            border-left: 4px solid #28a745;
        }

        .segment.speaker-2 {
            border-left: 4px solid #ffc107;
        }

        .segment.speaker-3 {
            border-left: 4px solid #dc3545;
        }

        .speaker-tag {
            font-weight: bold;
            color: #007bff;
            margin-right: 8px;
        }

        .time-tag {
            color: #6c757d;
            font-size: 0.9em;
            margin-right: 8px;
        }

        .timestamp-tag {
            font-size: 0.8em;
            color: #666;
            margin-right: 5px;
            background-color: #e9ecef;
            padding: 1px 4px;
            border-radius: 3px;
        }

        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin: 10px 0;
            height: 20px;
        }

        .progress-fill {
            height: 100%;
            background-color: #007bff;
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .api-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .api-info h3 {
            margin-top: 0;
            color: #0056b3;
        }

        .api-info code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        /* 实时转录历史记录样式 */
        .realtime-history {
            max-height: 350px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }

        .simple-segment {
            margin: 5px 0;
            padding: 8px;
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
            border-radius: 3px;
            word-wrap: break-word;
        }

        .realtime-stats {
            text-align: center;
            margin-top: 10px;
            color: #666;
            font-style: italic;
        }

        /* 滚动条样式 */
        .realtime-history::-webkit-scrollbar,
        .result::-webkit-scrollbar {
            width: 6px;
        }

        .realtime-history::-webkit-scrollbar-track,
        .result::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .realtime-history::-webkit-scrollbar-thumb,
        .result::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        .realtime-history::-webkit-scrollbar-thumb:hover,
        .result::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .segment, .simple-segment {
            animation: fadeIn 0.3s ease-out;
        }

        /* 演讲人管理样式 */
        .speaker-management {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .speaker-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            background-color: white;
        }

        .speaker-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
            margin-bottom: 5px;
        }

        .speaker-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .speaker-info {
            flex-grow: 1;
        }

        .speaker-name {
            font-weight: bold;
            color: #333;
        }

        .speaker-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 2px;
        }

        .speaker-actions {
            display: flex;
            gap: 5px;
        }

        .speaker-actions button {
            padding: 4px 8px;
            font-size: 12px;
            margin: 0;
        }

        .delete-btn {
            background-color: #dc3545;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .speaker-selection {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }

        .speaker-checkbox {
            margin: 5px 10px 5px 0;
        }

        .speaker-checkbox input[type="checkbox"] {
            margin-right: 5px;
        }

        .no-speakers {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎙️ 音频转文字服务</h1>

        <div class="api-info">
            <h3>📋 API 接口信息</h3>
            <p><strong>离线转录:</strong> <code>POST /transcribe</code> - 上传音频文件进行转录</p>
            <p><strong>离线转录 (WebSocket):</strong> <code>WebSocket /socket.io</code> - 支持大文件和进度反馈</p>
            <p><strong>实时转录:</strong> <code>WebSocket /socket.io</code> - 实时语音转文字</p>
            <p><strong>会话状态:</strong> <code>GET /api/sessions</code> - 查看当前会话状态</p>
            <p><strong>演讲人管理:</strong> <code>GET/POST/DELETE /api/speakers</code> - 管理演讲人音频</p>
        </div>

        <!-- 演讲人管理 -->
        <div class="speaker-management">
            <h2>👥 演讲人管理</h2>

            <!-- 添加演讲人 -->
            <div class="form-group">
                <h3>添加演讲人</h3>
                <form id="speakerForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="speakerName">演讲人名称:</label>
                        <input type="text" id="speakerName" name="name" required placeholder="请输入演讲人名称">
                    </div>
                    <div class="form-group">
                        <label for="speakerDescription">描述 (可选):</label>
                        <input type="text" id="speakerDescription" name="description" placeholder="请输入描述信息">
                    </div>
                    <div class="form-group">
                        <label for="speakerAudio">演讲人音频文件:</label>
                        <input type="file" id="speakerAudio" name="audio" accept=".wav,.mp3,.m4a,.aac,.flac,.ogg" required>
                        <small style="color: #666; display: block; margin-top: 5px;">
                            支持格式: WAV, MP3, M4A, AAC, FLAC, OGG | 建议时长: 3-10秒 | 最大: 50MB
                        </small>
                    </div>
                    <button type="submit" id="addSpeakerBtn">添加演讲人</button>
                </form>
            </div>

            <!-- 演讲人列表 -->
            <div class="form-group">
                <h3>已注册演讲人
                    <button type="button" id="refreshSpeakersBtn" style="font-size: 12px; padding: 4px 8px;">刷新</button>
                    <button type="button" id="cleanupCacheBtn" style="font-size: 12px; padding: 4px 8px; background-color: #ffc107;">清理缓存</button>
                    <button type="button" id="preloadSpeakersBtn" style="font-size: 12px; padding: 4px 8px; background-color: #28a745;">预加载</button>
                </h3>
                <div id="speakerList" class="speaker-list">
                    <div class="no-speakers">正在加载演讲人列表...</div>
                </div>
            </div>

            <!-- 内存状态显示 -->
            <div class="form-group">
                <h3>内存状态 <button type="button" id="refreshMemoryBtn" style="font-size: 12px; padding: 4px 8px;">刷新</button></h3>
                <div id="memoryStatus" style="font-size: 12px; color: #666; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                    <div>正在加载内存状态...</div>
                </div>
            </div>

            <!-- 演讲人选择区域 -->
            <div id="speakerSelection" class="speaker-selection" style="display: none;">
                <h4>选择要使用的演讲人:</h4>
                <div id="speakerCheckboxes"></div>
                <small style="color: #666;">
                    勾选后，系统将基于这些演讲人的音频特征进行说话人识别
                </small>
            </div>
        </div>

        <!-- 离线转录 -->
        <div class="section">
            <h2>📁 离线转录 (文件上传)</h2>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="audioFile">选择音频文件:</label>
                    <input type="file" id="audioFile" name="audio" accept="audio/*" required>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableSpeaker">
                        启用说话人分离
                    </label>
                </div>

                <div class="form-group" id="offlineSpeakerOptions" style="display: none;">
                    <label>
                        <input type="checkbox" id="useRegisteredSpeakers">
                        使用已注册演讲人进行识别
                    </label>
                    <div id="offlineSpeakerSelection" style="display: none; margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                        <small style="color: #666; display: block; margin-bottom: 10px;">选择要使用的演讲人:</small>
                        <div id="offlineSpeakerCheckboxes"></div>
                    </div>
                </div>

                <button type="submit">上传并转录</button>
            </form>

            <div id="uploadResult" class="result"></div>
        </div>

        <!-- 离线转录 (WebSocket方式) -->
        <div class="section">
            <h2>📁 离线转录 (WebSocket)</h2>
            <form id="offlineWebSocketForm">
                <div class="form-group">
                    <label for="offlineAudioFile">选择音频文件:</label>
                    <input type="file" id="offlineAudioFile" accept="audio/*" required>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableOfflineSpeaker">
                        启用说话人分离
                    </label>
                </div>

                <div class="form-group" id="offlineWsSpeakerOptions" style="display: none;">
                    <label>
                        <input type="checkbox" id="useOfflineRegisteredSpeakers">
                        使用已注册演讲人进行识别
                    </label>
                    <div id="offlineWsSpeakerSelection" style="display: none; margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                        <small style="color: #666; display: block; margin-bottom: 10px;">选择要使用的演讲人:</small>
                        <div id="offlineWsSpeakerCheckboxes"></div>
                    </div>
                </div>

                <button type="button" id="startOfflineBtn" onclick="startOfflineWebSocket()">开始WebSocket转录</button>
                <button type="button" id="cancelOfflineBtn" onclick="cancelOfflineWebSocket()" disabled>取消转录</button>
            </form>

            <!-- 进度条 -->
            <div id="offlineProgress" class="progress-container" style="display: none;">
                <div id="offlineProgressBar" class="progress-fill"></div>
            </div>
            <div id="offlineProgressText" style="display: none;"></div>

            <div id="offlineWebSocketResult" class="result"></div>
        </div>

        <!-- 实时转录 -->
        <div class="section">
            <h2>🎤 实时转录 (WebSocket)</h2>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enableRealtimeSpeaker">
                    启用说话人分离
                </label>
            </div>

            <div class="form-group" id="realtimeSpeakerOptions" style="display: none;">
                <label>
                    <input type="checkbox" id="useRealtimeRegisteredSpeakers">
                    使用已注册演讲人进行识别
                </label>
                <div id="realtimeSpeakerSelection" style="display: none; margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                    <small style="color: #666; display: block; margin-bottom: 10px;">选择要使用的演讲人:</small>
                    <div id="realtimeSpeakerCheckboxes"></div>
                </div>
            </div>

            <button id="startBtn" onclick="startRealtime()">开始实时转录</button>
            <button id="stopBtn" onclick="stopRealtime()" disabled>停止转录</button>
            <button id="clearBtn" onclick="clearRealtimeHistory()" disabled>清空历史</button>

            <div id="realtimeResult" class="result"></div>
        </div>

        <!-- 会话状态 -->
        <div class="section">
            <h2>📊 会话状态</h2>
            <button onclick="checkSessions()">查看会话状态</button>
            <div id="sessionResult" class="result"></div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="/static/js/realtime.js"></script>
    <script src="/static/js/offline-websocket.js"></script>
    <script>
        // 全局变量
        let speakers = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成');

            // 检查Socket.IO是否加载
            if (typeof io === 'undefined') {
                console.error('❌ Socket.IO未加载');
            } else {
                console.log('✅ Socket.IO已加载，版本:', io.version || 'unknown');
            }

            // 初始化演讲人管理
            initializeSpeakerManagement();

            // 检查必要的DOM元素
            const elements = [
                'offlineAudioFile',
                'enableOfflineSpeaker',
                'startOfflineBtn',
                'cancelOfflineBtn',
                'offlineWebSocketResult',
                'offlineProgress',
                'offlineProgressBar',
                'offlineProgressText',
                'enableRealtimeSpeaker',
                'startBtn',
                'stopBtn',
                'clearBtn',
                'realtimeResult'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    console.error(`❌ 缺少元素: ${id}`);
                } else {
                    console.log(`✅ 找到元素: ${id}`);
                }
            });
        });

        // 离线转录表单提交
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            const audioFile = document.getElementById('audioFile').files[0];
            const enableSpeaker = document.getElementById('enableSpeaker').checked;
            const useRegisteredSpeakers = document.getElementById('useRegisteredSpeakers').checked;

            if (!audioFile) {
                document.getElementById('uploadResult').innerHTML = '<div class="error">请选择音频文件</div>';
                return;
            }

            formData.append('audio', audioFile);
            formData.append('enable_speaker', enableSpeaker);
            formData.append('use_registered_speakers', useRegisteredSpeakers);

            // 如果使用已注册演讲人，添加选中的演讲人ID
            if (useRegisteredSpeakers) {
                const selectedSpeakerIds = getSelectedSpeakerIds('offlineSpeakerCheckboxes');
                if (selectedSpeakerIds.length > 0) {
                    formData.append('speaker_ids', selectedSpeakerIds.join(','));
                }
            }

            const resultDiv = document.getElementById('uploadResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在处理音频文件，请稍候...</div>';

            try {
                const response = await fetch('/transcribe', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.code === 200) {
                    let html = '<div class="success">✅ 转录成功</div>';

                    if (result.data.segmented_text && result.data.segmented_text.length > 0) {
                        html += '<div class="speaker-result">';
                        html += '<h3>🎭 说话人分离结果:</h3>';

                        result.data.segmented_text.forEach(segment => {
                            const speakerClass = segment.speaker.replace('_', '-').toLowerCase();
                            html += `<div class="segment ${speakerClass}">
                                <span class="speaker-tag">${segment.speaker}</span>
                                <span class="time-tag">(${segment.start_time.toFixed(1)}s-${segment.end_time.toFixed(1)}s)</span>:
                                ${segment.text}
                            </div>`;
                        });
                        html += '</div>';
                    } else {
                        html += `<p><strong>📝 转录结果:</strong></p><p>${result.data.voice_text}</p>`;
                    }

                    if (result.data.process_time) {
                        html += `<p><strong>⏱️ 处理时间:</strong> ${result.data.process_time}</p>`;
                    }

                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 转录失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        });

        // 查看会话状态
        async function checkSessions() {
            const resultDiv = document.getElementById('sessionResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在获取会话状态...</div>';

            try {
                const response = await fetch('/api/sessions');
                const result = await response.json();

                if (result.code === 200) {
                    let html = '<div class="success">✅ 会话状态获取成功</div>';
                    html += `<p><strong>📊 活跃会话数:</strong> ${result.data.active_sessions}</p>`;
                    html += `<p><strong>📈 最大并发数:</strong> ${result.data.max_concurrent_sessions}</p>`;
                    html += `<p><strong>⏰ 会话超时时间:</strong> ${Math.floor(result.data.max_idle_time / 60)}分钟</p>`;

                    if (result.data.sessions && result.data.sessions.length > 0) {
                        html += '<h4>🔗 当前会话列表:</h4>';
                        result.data.sessions.forEach(session => {
                            const lastActivity = new Date(session.last_activity).toLocaleString();
                            html += `<div class="info">
                                <strong>会话ID:</strong> ${session.session_id}<br>
                                <strong>类型:</strong> ${session.type}<br>
                                <strong>最后活动:</strong> ${lastActivity}<br>
                                <strong>说话人分离:</strong> ${session.enable_speaker ? '启用' : '禁用'}`;

                            if (session.type === 'realtime') {
                                html += `<br><strong>音频块数:</strong> ${session.audio_chunks_received}`;
                                html += `<br><strong>音频时长:</strong> ${session.total_audio_duration.toFixed(1)}秒`;
                            } else if (session.type === 'offline') {
                                html += `<br><strong>文件名:</strong> ${session.filename}`;
                                html += `<br><strong>上传进度:</strong> ${((session.total_size / session.expected_size) * 100).toFixed(1)}%`;
                            }

                            html += '</div>';
                        });
                    }

                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 获取失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 初始化演讲人管理
        function initializeSpeakerManagement() {
            loadSpeakers();
            loadMemoryStatus();
            setupSpeakerEventListeners();
        }

        // 设置演讲人管理事件监听器
        function setupSpeakerEventListeners() {
            // 演讲人表单提交
            const speakerForm = document.getElementById('speakerForm');
            if (speakerForm) {
                speakerForm.addEventListener('submit', addSpeaker);
            }

            // 刷新演讲人列表
            const refreshBtn = document.getElementById('refreshSpeakersBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', loadSpeakers);
            }

            // 清理缓存
            const cleanupBtn = document.getElementById('cleanupCacheBtn');
            if (cleanupBtn) {
                cleanupBtn.addEventListener('click', cleanupCache);
            }

            // 预加载演讲人
            const preloadBtn = document.getElementById('preloadSpeakersBtn');
            if (preloadBtn) {
                preloadBtn.addEventListener('click', preloadSpeakers);
            }

            // 刷新内存状态
            const refreshMemoryBtn = document.getElementById('refreshMemoryBtn');
            if (refreshMemoryBtn) {
                refreshMemoryBtn.addEventListener('click', loadMemoryStatus);
            }

            // 说话人分离选项变化
            const enableSpeaker = document.getElementById('enableSpeaker');
            if (enableSpeaker) {
                enableSpeaker.addEventListener('change', function() {
                    toggleSpeakerOptions('offlineSpeakerOptions', this.checked);
                });
            }

            const enableOfflineSpeaker = document.getElementById('enableOfflineSpeaker');
            if (enableOfflineSpeaker) {
                enableOfflineSpeaker.addEventListener('change', function() {
                    toggleSpeakerOptions('offlineWsSpeakerOptions', this.checked);
                });
            }

            const enableRealtimeSpeaker = document.getElementById('enableRealtimeSpeaker');
            if (enableRealtimeSpeaker) {
                enableRealtimeSpeaker.addEventListener('change', function() {
                    toggleSpeakerOptions('realtimeSpeakerOptions', this.checked);
                });
            }

            // 使用已注册演讲人选项变化
            const useRegisteredSpeakers = document.getElementById('useRegisteredSpeakers');
            if (useRegisteredSpeakers) {
                useRegisteredSpeakers.addEventListener('change', function() {
                    toggleSpeakerSelection('offlineSpeakerSelection', this.checked);
                });
            }

            const useOfflineRegisteredSpeakers = document.getElementById('useOfflineRegisteredSpeakers');
            if (useOfflineRegisteredSpeakers) {
                useOfflineRegisteredSpeakers.addEventListener('change', function() {
                    toggleSpeakerSelection('offlineWsSpeakerSelection', this.checked);
                });
            }

            const useRealtimeRegisteredSpeakers = document.getElementById('useRealtimeRegisteredSpeakers');
            if (useRealtimeRegisteredSpeakers) {
                useRealtimeRegisteredSpeakers.addEventListener('change', function() {
                    toggleSpeakerSelection('realtimeSpeakerSelection', this.checked);
                });
            }
        }

        // 切换演讲人选项显示
        function toggleSpeakerOptions(elementId, show) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = show ? 'block' : 'none';
            }
        }

        // 切换演讲人选择显示
        function toggleSpeakerSelection(elementId, show) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = show ? 'block' : 'none';
                if (show) {
                    updateSpeakerCheckboxes();
                }
            }
        }

        // 加载演讲人列表
        async function loadSpeakers() {
            try {
                const response = await fetch('/api/speakers');
                const result = await response.json();

                if (result.code === 200) {
                    speakers = result.data.speakers;
                    updateSpeakerList();
                    updateSpeakerCheckboxes();
                } else {
                    console.error('加载演讲人列表失败:', result.message);
                    const speakerList = document.getElementById('speakerList');
                    if (speakerList) {
                        speakerList.innerHTML = `<div class="no-speakers">加载失败: ${result.message}</div>`;
                    }
                }
            } catch (error) {
                console.error('加载演讲人列表错误:', error);
                const speakerList = document.getElementById('speakerList');
                if (speakerList) {
                    speakerList.innerHTML = '<div class="no-speakers">网络错误，无法加载演讲人列表</div>';
                }
            }
        }

        // 更新演讲人列表显示
        function updateSpeakerList() {
            const listElement = document.getElementById('speakerList');
            if (!listElement) return;

            if (speakers.length === 0) {
                listElement.innerHTML = '<div class="no-speakers">暂无已注册的演讲人</div>';
                return;
            }

            let html = '';
            speakers.forEach(speaker => {
                const duration = speaker.duration ? `${speaker.duration.toFixed(1)}秒` : '未知';
                const hasEmbedding = speaker.has_embedding ? '✅' : '❌';

                html += `
                    <div class="speaker-item">
                        <div class="speaker-info">
                            <div class="speaker-name">${speaker.name}</div>
                            <div class="speaker-details">
                                时长: ${duration} | 特征提取: ${hasEmbedding} |
                                ${speaker.description || '无描述'}
                            </div>
                        </div>
                        <div class="speaker-actions">
                            <button type="button" class="delete-btn" onclick="deleteSpeaker('${speaker.id}', '${speaker.name}')">
                                删除
                            </button>
                        </div>
                    </div>
                `;
            });

            listElement.innerHTML = html;
        }

        // 更新演讲人复选框
        function updateSpeakerCheckboxes() {
            const containers = [
                'offlineSpeakerCheckboxes',
                'offlineWsSpeakerCheckboxes',
                'realtimeSpeakerCheckboxes'
            ];

            containers.forEach(containerId => {
                const container = document.getElementById(containerId);
                if (!container) return;

                if (speakers.length === 0) {
                    container.innerHTML = '<div style="color: #666; font-style: italic;">暂无可选择的演讲人</div>';
                    return;
                }

                let html = '';
                speakers.forEach(speaker => {
                    const checkboxId = `${containerId}_${speaker.id}`;
                    html += `
                        <label class="speaker-checkbox">
                            <input type="checkbox" id="${checkboxId}" value="${speaker.id}">
                            ${speaker.name} (${speaker.duration ? speaker.duration.toFixed(1) + '秒' : '未知时长'})
                        </label>
                    `;
                });

                container.innerHTML = html;
            });
        }

        // 添加演讲人
        async function addSpeaker(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const addBtn = document.getElementById('addSpeakerBtn');

            // 验证输入
            const name = formData.get('name').trim();
            const audioFile = formData.get('audio');

            if (!name) {
                alert('请输入演讲人名称');
                return;
            }

            if (!audioFile || audioFile.size === 0) {
                alert('请选择音频文件');
                return;
            }

            // 检查文件大小 (50MB)
            if (audioFile.size > 50 * 1024 * 1024) {
                alert('音频文件不能超过50MB');
                return;
            }

            addBtn.disabled = true;
            addBtn.textContent = '添加中...';

            try {
                const response = await fetch('/api/speakers', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.code === 200) {
                    alert(`演讲人 "${name}" 添加成功！`);
                    form.reset();
                    loadSpeakers(); // 重新加载列表
                } else {
                    alert(`添加失败: ${result.message}`);
                }
            } catch (error) {
                console.error('添加演讲人错误:', error);
                alert(`添加失败: ${error.message}`);
            } finally {
                addBtn.disabled = false;
                addBtn.textContent = '添加演讲人';
            }
        }

        // 删除演讲人
        async function deleteSpeaker(speakerId, speakerName) {
            if (!confirm(`确定要删除演讲人 "${speakerName}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/speakers/${speakerId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.code === 200) {
                    alert(`演讲人 "${speakerName}" 删除成功！`);
                    loadSpeakers(); // 重新加载列表
                } else {
                    alert(`删除失败: ${result.message}`);
                }
            } catch (error) {
                console.error('删除演讲人错误:', error);
                alert(`删除失败: ${error.message}`);
            }
        }

        // 获取选中的演讲人ID列表
        function getSelectedSpeakerIds(containerId) {
            const container = document.getElementById(containerId);
            if (!container) return [];

            const checkboxes = container.querySelectorAll('input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 加载内存状态
        async function loadMemoryStatus() {
            try {
                const response = await fetch('/api/system/memory');
                const result = await response.json();

                if (result.code === 200) {
                    updateMemoryStatusDisplay(result.data);
                } else {
                    console.error('加载内存状态失败:', result.message);
                    document.getElementById('memoryStatus').innerHTML =
                        `<div style="color: #dc3545;">加载失败: ${result.message}</div>`;
                }
            } catch (error) {
                console.error('加载内存状态错误:', error);
                document.getElementById('memoryStatus').innerHTML =
                    '<div style="color: #dc3545;">网络错误，无法加载内存状态</div>';
            }
        }

        // 更新内存状态显示
        function updateMemoryStatusDisplay(data) {
            const memoryStatus = document.getElementById('memoryStatus');
            if (!memoryStatus) return;

            const memory = data.memory_status;
            const warnings = data.warnings || [];

            let html = '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">';

            // 系统内存
            html += '<div>';
            html += '<strong>系统内存:</strong><br>';
            html += `使用: ${memory.system.used_mb.toFixed(1)}MB / ${memory.system.total_mb.toFixed(1)}MB (${memory.system.percent.toFixed(1)}%)<br>`;
            html += `可用: ${memory.system.available_mb.toFixed(1)}MB`;
            html += '</div>';

            // 进程内存
            html += '<div>';
            html += '<strong>进程内存:</strong><br>';
            html += `RSS: ${memory.process.rss_mb.toFixed(1)}MB<br>`;
            html += `VMS: ${memory.process.vms_mb.toFixed(1)}MB`;
            html += '</div>';

            // 演讲人缓存
            html += '<div>';
            html += '<strong>演讲人缓存:</strong><br>';
            html += `缓存项: ${memory.cache.cache_size} / ${memory.cache.max_size}<br>`;
            html += `内存: ${memory.cache.memory_usage_mb.toFixed(1)}MB`;
            html += '</div>';

            // 阈值状态
            html += '<div>';
            html += '<strong>阈值状态:</strong><br>';
            const speakerMemory = memory.speaker_manager.memory_usage_mb || 0;
            const threshold = memory.thresholds.speaker_memory_threshold_mb;
            const isOverThreshold = speakerMemory > threshold;
            html += `演讲人内存: ${speakerMemory.toFixed(1)}MB / ${threshold}MB `;
            html += isOverThreshold ? '<span style="color: #dc3545;">⚠️</span>' : '<span style="color: #28a745;">✅</span>';
            html += '</div>';

            html += '</div>';

            // 显示警告
            if (warnings.length > 0) {
                html += '<div style="margin-top: 10px; padding: 8px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">';
                html += '<strong style="color: #856404;">⚠️ 警告:</strong><br>';
                warnings.forEach(warning => {
                    html += `• ${warning}<br>`;
                });
                html += '</div>';
            }

            memoryStatus.innerHTML = html;
        }

        // 清理缓存
        async function cleanupCache() {
            if (!confirm('确定要清理演讲人特征缓存吗？这将释放内存但可能影响识别性能。')) {
                return;
            }

            const cleanupBtn = document.getElementById('cleanupCacheBtn');
            const originalText = cleanupBtn.textContent;

            cleanupBtn.disabled = true;
            cleanupBtn.textContent = '清理中...';

            try {
                const response = await fetch('/api/speakers/cleanup', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.code === 200) {
                    const cleaned = result.data.cleaned;
                    alert(`缓存清理成功！\n释放了 ${cleaned.cache_items} 个缓存项\n节省了 ${cleaned.memory_mb.toFixed(1)}MB 内存`);
                    loadMemoryStatus(); // 刷新内存状态
                } else {
                    alert(`清理失败: ${result.message}`);
                }
            } catch (error) {
                console.error('清理缓存错误:', error);
                alert(`清理失败: ${error.message}`);
            } finally {
                cleanupBtn.disabled = false;
                cleanupBtn.textContent = originalText;
            }
        }

        // 预加载演讲人特征
        async function preloadSpeakers() {
            const preloadBtn = document.getElementById('preloadSpeakersBtn');
            const originalText = preloadBtn.textContent;

            preloadBtn.disabled = true;
            preloadBtn.textContent = '预加载中...';

            try {
                const response = await fetch('/api/speakers/preload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({}) // 预加载所有演讲人
                });

                const result = await response.json();

                if (result.code === 200) {
                    alert(`预加载完成！\n成功加载了 ${result.data.loaded_count} 个演讲人特征`);
                    loadMemoryStatus(); // 刷新内存状态
                } else {
                    alert(`预加载失败: ${result.message}`);
                }
            } catch (error) {
                console.error('预加载错误:', error);
                alert(`预加载失败: ${error.message}`);
            } finally {
                preloadBtn.disabled = false;
                preloadBtn.textContent = originalText;
            }
        }
    </script>
</body>
</html>